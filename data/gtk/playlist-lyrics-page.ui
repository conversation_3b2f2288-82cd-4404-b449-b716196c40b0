<?xml version="1.0" encoding="UTF-8"?>
<interface>
    <requires lib="gtk" version="4.0" />
    <requires lib="libadwaita" version="1.0" />
    <template class="PlayListLyricsPage" parent="AdwBin">
        <property name="child">
            <object class="AdwFlap">
                <property name="flap-position">end</property>
                <property name="content">
                    <object class="SongListView" id="songs_list">
                        <property name="width-request">500</property>
                        <property name="clamp-maximum-size">1000</property>
                        <property name="clamp-tightening-threshold">730</property>
                    </object>
                </property>
                <property name="flap">
                    <object class="AdwClamp">
                        <property name="vexpand">true</property>
                        <property name="hexpand">true</property>
                        <property name="halign">fill</property>
                        <property name="margin-top">10</property>
                        <property name="margin-bottom">10</property>
                        <property name="margin-start">20</property>
                        <property name="margin-end">20</property>
                        <property name="maximum-size">1000</property>
                        <property name="tightening-threshold">730</property>
                        <child>
                            <object class="GtkScrolledWindow" id="scroll_lyrics_win">
                                <property name="width-request">500</property>
                                <child>
                                    <object class="GtkTextView" id="lyrics_text_view">
                                        <property name="hscroll-policy">natural</property>
                                        <property name="vscroll-policy">natural</property>
                                        <property name="pixels-below-lines">15</property>
                                        <property name="editable">False</property>
                                        <property name="justification">center</property>
                                        <property name="left-margin">8</property>
                                        <property name="right-margin">8</property>
                                        <property name="top-margin">18</property>
                                        <property name="bottom_margin">18</property>
                                        <property name="cursor-visible">False</property>
                                        <property name="accepts-tab">False</property>
                                        <property name="buffer">
                                            <object class="GtkTextBuffer" id="buffer">
                                                <property name="tag_table">
                                                    <object class="GtkTextTagTable">
                                                        <child type="tag">
                                                            <object class="GtkTextTag"
                                                                id="highlight_text_tag">
                                                                <property name="foreground">#00d4ff</property>
                                                                <property name="weight">700</property>
                                                                <property name="size">17160</property>
                                                            </object>
                                                        </child>
                                                    </object>
                                                </property>
                                            </object>
                                        </property>
                                    </object>
                                </child>
                            </object>
                        </child>
                    </object>
                </property>
            </object>
        </property>
    </template>
</interface>