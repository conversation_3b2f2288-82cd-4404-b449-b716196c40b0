<?xml version="1.0" encoding="UTF-8"?>
<interface>
    <requires lib="gtk" version="4.0" />
    <template class="Discover" parent="GtkBox">
        <property name="valign">start</property>
        <property name="halign">center</property>
        <property name="orientation">vertical</property>
        <child>
            <object class="GtkBox">
                <property name="halign">fill</property>
                <property name="valign">start</property>
                <property name="orientation">vertical</property>
                <property name="spacing">12</property>
                <child>
                    <object class="GtkOverlay">
                        <property name="margin-top">13</property>
                        <property name="halign">center</property>
                        <property name="valign">fill</property>
                        <property name="height-request">283</property>
                        <property name="vexpand">true</property>
                        <child>
                            <object class="AdwCarousel" id="carousel">
                                <property name="halign">center</property>
                                <property name="valign">start</property>
                                <property name="allow-scroll-wheel">false</property>
                                <signal name="notify::position" handler="carousel_notify_position_cb" swapped="true" />
                                <child>
                                    <object class="GtkGestureClick">
                                        <signal name="pressed" handler="carousel_pressed_cb" swapped="true" />
                                    </object>
                                </child>
                                <style>
                                    <class name="card" />
                                </style>
                            </object>
                        </child>
                        <child type="overlay">
                            <object class="GtkButton" id="previous_button">
                                <property name="can_focus">False</property>
                                <property name="halign">start</property>
                                <property name="valign">center</property>
                                <property name="width-request">39</property>
                                <property name="height-request">39</property>
                                <property name="margin-top">9</property>
                                <property name="margin-bottom">9</property>
                                <property name="margin-start">9</property>
                                <property name="margin-end">9</property>
                                <property name="icon-name">go-previous-symbolic</property>
                                <signal name="clicked" handler="previous_button_clicked_cb" swapped="true" />
                                <style>
                                    <class name="circular" />
                                    <class name="flat" />
                                    <class name="image-button" />
                                    <class name="hover-button" />
                                </style>
                            </object>
                        </child>
                        <child type="overlay">
                            <object class="GtkButton" id="next_button">
                                <property name="can_focus">False</property>
                                <property name="halign">end</property>
                                <property name="valign">center</property>
                                <property name="width-request">39</property>
                                <property name="height-request">39</property>
                                <property name="margin-top">9</property>
                                <property name="margin-bottom">9</property>
                                <property name="margin-start">9</property>
                                <property name="margin-end">9</property>
                                <property name="icon-name">go-next-symbolic</property>
                                <signal name="clicked" handler="next_button_clicked_cb" swapped="true" />
                                <style>
                                    <class name="circular" />
                                    <class name="flat" />
                                    <class name="image-button" />
                                    <class name="hover-button" />
                                </style>
                            </object>
                        </child>
                    </object>
                </child>
                <child>
                    <object class="AdwCarouselIndicatorDots">
                        <property name="carousel">carousel</property>
                        <property name="valign">start</property>
                        <property name="vexpand">false</property>
                    </object>
                </child>
            </object>
        </child>
        <child>
            <object class="GtkBox">
                <property name="halign">fill</property>
                <property name="valign">start</property>
                <property name="orientation">vertical</property>
                <property name="spacing">12</property>
                <property name="hexpand">true</property>
                <property name="height-request">-1</property>
                <child>
                    <object class="GtkBox">
                        <property name="halign">fill</property>
                        <property name="valign">fill</property>
                        <property name="orientation">vertical</property>
                        <property name="spacing">12</property>
                        <property name="hexpand">true</property>
                        <child>
                            <object class="GtkBox">
                                <property name="halign">fill</property>
                                <property name="valign">fill</property>
                                <property name="orientation">horizontal</property>
                                <child>
                                    <object class="GtkImage">
                                        <property name="halign">start</property>
                                        <property name="valign">center</property>
                                        <property name="icon-name">media-optical-cd-audio-symbolic</property>
                                    </object>
                                </child>
                                <child>
                                    <object class="GtkLabel">
                                        <property name="halign">start</property>
                                        <property name="valign">center</property>
                                        <property name="margin-start">9</property>
                                        <property name="label" translatable="yes">Top Picks</property>
                                        <attributes>
                                            <attribute name="size" value="15000" />
                                        </attributes>
                                    </object>
                                </child>
                                <child>
                                    <object class="GtkButton">
                                        <property name="halign">end</property>
                                        <property name="valign">end</property>
                                        <property name="hexpand">true</property>
                                        <property name="icon-name">view-more-symbolic</property>
                                        <property name="tooltip-text" translatable="yes">View More</property>
                                        <signal name="clicked" handler="top_picks_cb" swapped="true" />
                                        <style>
                                            <class name="flat" />
                                            <class name="image-button" />
                                        </style>
                                    </object>
                                </child>
                            </object>
                        </child>
                        <child>
                            <object class="GtkSeparator">
                                <property name="valign">start</property>
                                <property name="sensitive">False</property>
                                <property name="can_focus">False</property>
                            </object>
                        </child>
                        <child>
                            <object class="GtkFlowBox" id="top_picks">
                                <property name="hexpand">True</property>
                                <property name="vexpand">True</property>
                                <property name="max-children-per-line">4</property>
                                <property name="min-children-per-line">3</property>
                                <property name="homogeneous">False</property>
                                <property name="selection-mode">none</property>
                                <property name="activate-on-single-click">True</property>
                            </object>
                        </child>
                    </object>
                </child>
                <child>
                    <object class="GtkBox">
                        <property name="halign">fill</property>
                        <property name="valign">fill</property>
                        <property name="orientation">vertical</property>
                        <property name="spacing">12</property>
                        <property name="margin-top">12</property>
                        <property name="hexpand">true</property>
                        <child>
                            <object class="GtkBox">
                                <property name="halign">fill</property>
                                <property name="valign">fill</property>
                                <property name="orientation">horizontal</property>
                                <child>
                                    <object class="GtkImage">
                                        <property name="halign">start</property>
                                        <property name="valign">center</property>
                                        <property name="icon-name">media-optical-cd-audio-symbolic</property>
                                    </object>
                                </child>
                                <child>
                                    <object class="GtkLabel">
                                        <property name="halign">start</property>
                                        <property name="valign">center</property>
                                        <property name="margin-start">9</property>
                                        <property name="label" translatable="yes">New Albums</property>
                                        <attributes>
                                            <attribute name="size" value="15000" />
                                        </attributes>
                                    </object>
                                </child>
                                <child>
                                    <object class="GtkButton">
                                        <property name="halign">end</property>
                                        <property name="valign">end</property>
                                        <property name="hexpand">true</property>
                                        <property name="icon-name">view-more-symbolic</property>
                                        <property name="tooltip-text" translatable="yes">View More</property>
                                        <signal name="clicked" handler="new_albums_cb" swapped="true" />
                                        <style>
                                            <class name="flat" />
                                            <class name="image-button" />
                                        </style>
                                    </object>
                                </child>
                            </object>
                        </child>
                        <child>
                            <object class="GtkSeparator">
                                <property name="valign">start</property>
                                <property name="sensitive">False</property>
                                <property name="can_focus">False</property>
                            </object>
                        </child>
                        <child>
                            <object class="GtkFlowBox" id="new_albums">
                                <property name="hexpand">True</property>
                                <property name="vexpand">True</property>
                                <property name="max-children-per-line">4</property>
                                <property name="min-children-per-line">3</property>
                                <property name="homogeneous">False</property>
                                <property name="selection-mode">none</property>
                                <property name="activate-on-single-click">True</property>
                            </object>
                        </child>
                    </object>
                </child>
            </object>
        </child>
    </template>
</interface>
