<?xml version="1.0" encoding="UTF-8"?>
<interface>
    <requires lib="gtk" version="4.0" />
    <template class="SongListView" parent="GtkBox">
        <child>
            <object class="GtkScrolledWindow" id="scroll_win">
                <child>
                    <object class="GtkViewport">
                        <property name="scroll-to-focus">True</property>
                        <child>
                            <object class="AdwClamp" id="adw_clamp">
                                <property name="vexpand">true</property>
                                <property name="hexpand">true</property>
                                <property name="maximum-size">1000</property>
                                <property name="tightening-threshold">730</property>
                                <property name="margin-top">10</property>
                                <property name="margin-bottom">10</property>
                                <property name="margin-start">20</property>
                                <property name="margin-end">20</property>
                                <child>
                                    <object class="GtkListBox" id="listbox">
                                        <property name="valign">start</property>
                                        <style>
                                            <class name="boxed-list" />
                                        </style>
                                    </object>
                                </child>
                            </object>
                        </child>
                    </object>
                </child>
            </object>
        </child>
    </template>
</interface>
