<?xml version="1.0" encoding="UTF-8"?>
<interface>
    <requires lib="gtk" version="4.0" />
    <template class="MyPage" parent="GtkBox">
        <property name="valign">fill</property>
        <property name="halign">center</property>
        <property name="orientation">vertical</property>
        <property name="spacing">30</property>
        <child>
            <object class="AdwClamp">
                <property name="halign">fill</property>
                <property name="hexpand">true</property>
                <property name="margin-top">30</property>
                <property name="maximum-size">1000</property>
                <property name="tightening-threshold">730</property>
                <child>
                    <object class="GtkBox">
                        <property name="halign">fill</property>
                        <property name="valign">start</property>
                        <property name="orientation">horizontal</property>
                        <property name="spacing">58</property>
                        <child>
                            <object class="GtkBox">
                                <property name="orientation">vertical</property>
                                <property name="visible">false</property>
                                <child>
                                    <object class="AdwAvatar" id="daily_rec_avatar">
                                        <property name="icon-name">x-office-calendar-symbolic</property>
                                        <property name="size">100</property>
                                        <child>
                                            <object class="GtkGestureClick">
                                                <signal name="pressed" handler="daily_rec_cb" swapped="true" />
                                            </object>
                                        </child>
                                    </object>
                                </child>
                                <child>
                                    <object class="GtkLabel">
                                        <property name="halign">start</property>
                                        <property name="valign">center</property>
                                        <property name="margin-start">9</property>
                                        <property name="label" translatable="yes">Daily Recommendation</property>
                                        <attributes>
                                            <attribute name="size" value="15000" />
                                        </attributes>
                                    </object>
                                </child>
                            </object>
                        </child>
                        <child>
                            <object class="GtkBox">
                                <property name="orientation">vertical</property>
                                <child>
                                    <object class="AdwAvatar">
                                        <property name="icon-name">emote-love-symbolic</property>
                                        <property name="size">100</property>
                                        <child>
                                            <object class="GtkGestureClick">
                                                <signal name="pressed" handler="heartbeat_cb" swapped="true" />
                                            </object>
                                        </child>
                                    </object>
                                </child>
                                <child>
                                    <object class="GtkLabel">
                                        <property name="halign">start</property>
                                        <property name="valign">center</property>
                                        <property name="margin-start">9</property>
                                        <property name="label" translatable="yes">Favorite Songs</property>
                                        <attributes>
                                            <attribute name="size" value="15000" />
                                        </attributes>
                                    </object>
                                </child>
                            </object>
                        </child>
                        <child>
                            <object class="GtkBox">
                                <property name="orientation">vertical</property>
                                <child>
                                    <object class="AdwAvatar">
                                        <property name="icon-name">audio-headphones-symbolic</property>
                                        <property name="size">100</property>
                                        <child>
                                            <object class="GtkGestureClick">
                                                <signal name="pressed" handler="radio_cb" swapped="true" />
                                            </object>
                                        </child>
                                    </object>
                                </child>
                                <child>
                                    <object class="GtkLabel">
                                        <property name="halign">start</property>
                                        <property name="valign">center</property>
                                        <property name="margin-start">9</property>
                                        <property name="label" translatable="yes">My Radio</property>
                                        <attributes>
                                            <attribute name="size" value="15000" />
                                        </attributes>
                                    </object>
                                </child>
                            </object>
                        </child>
                        <child>
                            <object class="GtkBox">
                                <property name="orientation">vertical</property>
                                <child>
                                    <object class="AdwAvatar">
                                        <property name="icon-name">weather-overcast-symbolic</property>
                                        <property name="size">100</property>
                                        <child>
                                            <object class="GtkGestureClick">
                                                <signal name="pressed" handler="cloud_disk_cb" swapped="true" />
                                            </object>
                                        </child>
                                    </object>
                                </child>
                                <child>
                                    <object class="GtkLabel">
                                        <property name="halign">start</property>
                                        <property name="valign">center</property>
                                        <property name="margin-start">9</property>
                                        <property name="label" translatable="yes">Cloud Music</property>
                                        <attributes>
                                            <attribute name="size" value="15000" />
                                        </attributes>
                                    </object>
                                </child>
                            </object>
                        </child>
                        <child>
                            <object class="GtkBox">
                                <property name="orientation">vertical</property>
                                <child>
                                    <object class="AdwAvatar">
                                        <property name="icon-name">media-optical-bd-symbolic</property>
                                        <property name="size">100</property>
                                        <child>
                                            <object class="GtkGestureClick">
                                                <signal name="pressed" handler="collection_album_cb" swapped="true" />
                                            </object>
                                        </child>
                                    </object>
                                </child>
                                <child>
                                    <object class="GtkLabel">
                                        <property name="halign">start</property>
                                        <property name="valign">center</property>
                                        <property name="margin-start">9</property>
                                        <property name="label" translatable="yes">Favorite Album</property>
                                        <attributes>
                                            <attribute name="size" value="15000" />
                                        </attributes>
                                    </object>
                                </child>
                            </object>
                        </child>
                        <child>
                            <object class="GtkBox">
                                <property name="orientation">vertical</property>
                                <child>
                                    <object class="AdwAvatar">
                                        <property name="icon-name">non-starred-symbolic</property>
                                        <property name="size">100</property>
                                        <child>
                                            <object class="GtkGestureClick">
                                                <signal name="pressed" handler="collection_songlist_cb" swapped="true" />
                                            </object>
                                        </child>
                                    </object>
                                </child>
                                <child>
                                    <object class="GtkLabel">
                                        <property name="halign">start</property>
                                        <property name="valign">center</property>
                                        <property name="margin-start">9</property>
                                        <property name="label" translatable="yes">Favorite Song List</property>
                                        <attributes>
                                            <attribute name="size" value="15000" />
                                        </attributes>
                                    </object>
                                </child>
                            </object>
                        </child>
                    </object>
                </child>
            </object>
        </child>
        <child>
            <object class="GtkBox">
                <property name="halign">fill</property>
                <property name="valign">fill</property>
                <property name="orientation">vertical</property>
                <property name="spacing">12</property>
                <property name="hexpand">true</property>
                <child>
                    <object class="GtkBox">
                        <property name="halign">fill</property>
                        <property name="valign">fill</property>
                        <property name="orientation">horizontal</property>
                        <child>
                            <object class="GtkImage">
                                <property name="halign">start</property>
                                <property name="valign">center</property>
                                <property name="icon-name">media-optical-cd-audio-symbolic</property>
                            </object>
                        </child>
                        <child>
                            <object class="GtkLabel">
                                <property name="halign">start</property>
                                <property name="valign">center</property>
                                <property name="margin-start">9</property>
                                <property name="label" translatable="yes">Recommended Song List</property>
                                <attributes>
                                    <attribute name="size" value="15000" />
                                </attributes>
                            </object>
                        </child>
                    </object>
                </child>
                <child>
                    <object class="GtkBox">
                        <property name="orientation">vertical</property>
                        <child>
                            <object class="GtkSeparator">
                                <property name="valign">start</property>
                                <property name="sensitive">False</property>
                                <property name="can_focus">False</property>
                            </object>
                        </child>
                        <child>
                            <object class="GtkScrolledWindow">
                                <child>
                                    <object class="GtkViewport">
                                        <child>
                                            <object class="AdwClamp">
                                                <property name="vexpand">true</property>
                                                <property name="hexpand">true</property>
                                                <property name="maximum-size">1000</property>
                                                <property name="margin-top">20</property>
                                                <property name="margin-bottom">20</property>
                                                <property name="tightening-threshold">730</property>
                                                <child>
                                                    <object class="GtkFlowBox" id="rec_grid">
                                                        <property name="hexpand">True</property>
                                                        <property name="valign">start</property>
                                                        <property name="max-children-per-line">4</property>
                                                        <property name="min-children-per-line">3</property>
                                                        <property name="homogeneous">False</property>
                                                        <property name="selection-mode">none</property>
                                                        <property name="activate-on-single-click">True</property>
                                                    </object>
                                                </child>
                                            </object>
                                        </child>
                                    </object>
                                </child>
                            </object>
                        </child>
                    </object>
                </child>
            </object>
        </child>
    </template>
</interface>
