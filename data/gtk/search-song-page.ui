<?xml version="1.0" encoding="UTF-8"?>
<interface>
    <requires lib="gtk" version="4.0" />
    <template class="SearchSongPage" parent="GtkBox">
        <property name="valign">fill</property>
        <property name="halign">fill</property>
        <property name="orientation">vertical</property>
        <property name="spacing">20</property>
        <child>
            <object class="AdwClamp" id="title_clamp">
                <property name="hexpand">true</property>
                <property name="margin-start">20</property>
                <property name="margin-end">20</property>
                <property name="maximum-size">1000</property>
                <property name="tightening-threshold">730</property>
                <child>
                    <object class="GtkBox">
                        <property name="halign">fill</property>
                        <property name="valign">fill</property>
                        <property name="hexpand">true</property>
                        <property name="orientation">horizontal</property>
                        <property name="spacing">12</property>
                        <child>
                            <object class="GtkBox">
                                <property name="orientation">vertical</property>
                                <property name="halign">fill</property>
                                <property name="valign">end</property>
                                <property name="spacing">2</property>
                                <child>
                                    <object class="GtkLabel" id="title_label">
                                        <property name="label">Title</property>
                                        <property name="halign">start</property>
                                        <property name="valign">end</property>
                                        <property name="max-width-chars">27</property>
                                        <property name="ellipsize">end</property>
                                        <property name="margin-top">20</property>
                                        <style>
                                            <class name="title-1" />
                                        </style>
                                    </object>
                                </child>
                                <child>
                                    <object class="GtkBox">
                                        <property name="halign">fill</property>
                                        <property name="valign">end</property>
                                        <property name="spacing">7</property>
                                        <property name="orientation">horizontal</property>
                                        <child>
                                            <object class="GtkLabel" id="num_label">
                                                <property name="label" translatable="yes">0 songs</property>
                                                <property name="halign">start</property>
                                                <property name="valign">end</property>
                                            </object>
                                        </child>
                                        <child>
                                            <object class="GtkButton" id="play_button">
                                                <property name="halign">end</property>
                                                <property name="valign">center</property>
                                                <property name="hexpand">true</property>
                                                <property name="icon-name">media-playback-start-symbolic</property>
                                                <signal name="clicked" handler="play_button_clicked_cb" swapped="true" />
                                                <property name="tooltip-text" translatable="yes">Play songs list</property>
                                                <style>
                                                    <class name="circular" />
                                                </style>
                                            </object>
                                        </child>
                                    </object>
                                </child>
                            </object>
                        </child>
                    </object>
                </child>
            </object>
        </child>
        <child>
            <object class="SongListView" id="songs_list"></object>
        </child>
    </template>
</interface>
