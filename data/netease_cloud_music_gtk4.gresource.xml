<?xml version="1.0" encoding="UTF-8"?>
<gresources>
    <gresource prefix="/com/gitee/gmg137/NeteaseCloudMusicGtk4">
        <file compressed="true">gtk/window.ui</file>
        <file compressed="true">gtk/preferences.ui</file>
        <file compressed="true">gtk/theme-selector.ui</file>
        <file compressed="true">gtk/discover.ui</file>
        <file compressed="true">gtk/my-page.ui</file>
        <file compressed="true">gtk/help-overlay.ui</file>
        <file compressed="true">gtk/user-menus.ui</file>
        <file compressed="true">gtk/player-controls.ui</file>
        <file compressed="true">gtk/songlist-page.ui</file>
        <file compressed="true">gtk/songlist-row.ui</file>
        <file compressed="true">gtk/songlist-view.ui</file>
        <file compressed="true">gtk/toplist.ui</file>
        <file compressed="true">gtk/search-song-page.ui</file>
        <file compressed="true">gtk/search-songlist-page.ui</file>
        <file compressed="true">gtk/search-singer-page.ui</file>
        <file compressed="true">gtk/playlist-lyrics-page.ui</file>
        <file compressed="true">themes/themesselector.css</file>
        <file compressed="true">themes/discover.css</file>
    </gresource>
    <gresource prefix="/com/gitee/gmg137/NeteaseCloudMusicGtk4/icons/scalable/apps/">
        <file preprocess="xml-stripblanks" alias="logo.svg">icons/hicolor/scalable/apps/com.gitee.gmg137.NeteaseCloudMusicGtk4.svg</file>
    </gresource>
    <gresource prefix="/com/gitee/gmg137/NeteaseCloudMusicGtk4/icons/symbolic/apps/">
        <file preprocess="xml-stripblanks" alias="logo-symbolic.svg">icons/hicolor/symbolic/apps/com.gitee.gmg137.NeteaseCloudMusicGtk4-symbolic.svg</file>
    </gresource>
</gresources>
