use reqwest;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize)]
struct SongRequest {
    url: String,
    level: String,
    #[serde(rename = "type")]
    response_type: String,
}

#[derive(Debug, Deserialize)]
struct SongResponse {
    data: Option<SongData>,
    message: String,
    status: u16,
    success: bool,
}

#[derive(Debug, Deserialize)]
struct SongData {
    al_name: String,
    ar_name: String,
    id: String,
    level: String,
    lyric: String,
    name: String,
    pic: String,
    size: String,
    tlyric: String,
    url: String,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    
    let request_data = SongRequest {
        url: "112121".to_string(),
        level: "jymaster".to_string(),
        response_type: "json".to_string(),
    };

    println!("正在测试API: http://192.168.90.20:8866/Song_V1");
    println!("请求参数: {:?}", request_data);

    let response = client
        .post("http://192.168.90.20:8866/Song_V1")
        .form(&request_data)
        .send()
        .await?;

    println!("响应状态: {}", response.status());

    if response.status().is_success() {
        let song_response: SongResponse = response.json().await?;
        println!("API响应: {:#?}", song_response);
        
        if song_response.success && song_response.status == 200 {
            if let Some(data) = song_response.data {
                println!("成功获取歌曲信息:");
                println!("  歌曲名: {}", data.name);
                println!("  歌手: {}", data.ar_name);
                println!("  专辑: {}", data.al_name);
                println!("  播放链接: {}", data.url);
                println!("  文件大小: {}", data.size);
            } else {
                println!("API返回成功但没有数据");
            }
        } else {
            println!("API返回失败: {}", song_response.message);
        }
    } else {
        let error_text = response.text().await?;
        println!("请求失败: {}", error_text);
    }

    Ok(())
}
