# 网易云音乐GTK4 - 新API调试指南

## 概述

已成功将获取歌曲播放链接的API替换为自定义API端点：
- **API地址**: `http://192.168.90.20:8866/Song_V1`
- **请求方法**: POST
- **参数格式**: `url=歌曲ID&level=jymaster&type=json`

## 调试功能

### 1. 详细的Debug日志

现在应用程序包含了详细的debug信息，涵盖整个获取播放链接的流程：

#### 在 `src/ncmapi.rs` 中的日志：
- 开始获取歌曲播放链接的请求
- API请求参数详情
- HTTP响应状态
- JSON响应解析结果
- 歌曲信息（歌名、歌手、专辑、文件大小）
- 播放链接获取成功/失败状态

#### 在 `src/application.rs` 中的日志：
- 播放请求处理开始
- 缓存文件检查
- API调用结果处理
- 播放链接设置

### 2. 如何查看Debug信息

#### 方法1：使用提供的脚本
```bash
./run_with_debug.sh
```

这个脚本会：
- 启动应用程序并显示过滤后的debug信息
- 使用颜色高亮重要信息
- 实时显示API调用过程

#### 方法2：手动运行
```bash
RUST_LOG=debug ./target/debug/netease-cloud-music-gtk4
```

#### 方法3：只查看特定模块的日志
```bash
RUST_LOG=netease_cloud_music_gtk4::ncmapi=debug ./target/debug/netease-cloud-music-gtk4
```

### 3. 典型的Debug输出示例

当你播放一首歌时，会看到类似以下的日志：

```
DEBUG 歌曲 '一生之中只需给我一次幸运' (ID: 112121) 需要获取播放链接，音质设置: 320000
DEBUG 开始获取歌曲播放链接，歌曲ID列表: [112121]
DEBUG 正在处理歌曲ID: 112121
DEBUG 准备发送API请求到: http://192.168.90.20:8866/Song_V1
DEBUG 请求参数: SongRequest { url: "112121", level: "jymaster", response_type: "json" }
DEBUG 收到HTTP响应，状态码: 200 OK
DEBUG HTTP请求成功，开始解析JSON响应
DEBUG API响应解析完成: success=true, status=200, message='获取歌曲信息成功'
DEBUG 获取到歌曲数据: 歌曲名='一生之中只需给我一次幸运', 歌手='罗文', 专辑='戏假情真', 文件大小='182.62MB'
DEBUG 成功获取播放链接: http://m801.music.126.net/...
DEBUG 已添加到结果列表，当前结果数量: 1
DEBUG 歌曲播放链接获取完成，成功获取 1 个链接
DEBUG 成功调用songs_url API，返回结果: [SongUrl { id: 112121, url: "http://...", rate: 320000 }]
DEBUG 使用播放链接: http://... 开始播放歌曲 '一生之中只需给我一次幸运'
```

### 4. 故障排除

#### 如果看到网络错误：
```
ERROR 调用songs_url API失败: ... - 歌曲 '...' (ID: ...)
```
- 检查API服务器是否运行在 `http://192.168.90.20:8866`
- 检查网络连接
- 确认防火墙设置

#### 如果看到API返回错误：
```
DEBUG API响应解析完成: success=false, status=..., message='...'
```
- 检查API服务器的响应格式
- 确认歌曲ID是否有效
- 检查API服务器日志

#### 如果看到空的播放链接：
```
DEBUG 获取歌曲播放链接失败，API返回空URL: ...
```
- 检查API响应中的`data.url`字段
- 确认API服务器能正确处理请求的歌曲

## 构建和运行

### 构建项目：
```bash
cargo build
```

### 运行应用程序：
```bash
# 普通运行
./target/debug/netease-cloud-music-gtk4

# 带debug信息运行
RUST_LOG=debug ./target/debug/netease-cloud-music-gtk4

# 使用调试脚本
./run_with_debug.sh
```

## API格式说明

### 请求格式：
```
POST http://192.168.90.20:8866/Song_V1
Content-Type: application/x-www-form-urlencoded

url=112121&level=jymaster&type=json
```

### 响应格式：
```json
{
    "data": {
        "al_name": "专辑名",
        "ar_name": "歌手名",
        "id": "112121",
        "level": "jymaster",
        "lyric": "歌词内容...",
        "name": "歌曲名",
        "pic": "封面图片URL",
        "size": "文件大小",
        "tlyric": "翻译歌词",
        "url": "播放链接URL"
    },
    "message": "获取歌曲信息成功",
    "status": 200,
    "success": true
}
```

现在你可以通过详细的debug信息来监控和调试新API的调用过程！
