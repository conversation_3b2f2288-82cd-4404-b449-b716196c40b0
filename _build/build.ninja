# This is the build file for project "netease-cloud-music-gtk4"
# It is autogenerated by the Meson build system.
# Do not edit by hand.

ninja_required_version = 1.8.2

# Rules for module scanning.

# Rules for compiling.

# Rules for linking.

# Other rules

rule CUSTOM_COMMAND
 command = $COMMAND
 description = $DESC
 restat = 1

rule CUSTOM_COMMAND_DEP
 command = $COMMAND
 deps = gcc
 depfile = $DEPFILE_UNQUOTED
 description = $DESC
 restat = 1

rule REGENERATE_BUILD
 command = /usr/bin/meson --internal regenerate /home/<USER>/Videos/netease-cloud-music-gtk .
 description = Regenerating build files.
 generator = 1

# Phony build target, always out of date

build PHONY: phony 

# Build rules for targets

build data/netease-cloud-music-gtk4.gresource: CUSTOM_COMMAND_DEP ../data/netease_cloud_music_gtk4.gresource.xml | /usr/bin/glib-compile-resources
 DEPFILE = data/netease-cloud-music-gtk4.gresource.d
 DEPFILE_UNQUOTED = data/netease-cloud-music-gtk4.gresource.d
 COMMAND = /usr/bin/glib-compile-resources ../data/netease_cloud_music_gtk4.gresource.xml --sourcedir ../data --internal --generate --target data/netease-cloud-music-gtk4.gresource --dependency-file data/netease-cloud-music-gtk4.gresource.d
 description = Generating$ data/netease-cloud-music-gtk4_gresource$ with$ a$ custom$ command

build data/com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop: CUSTOM_COMMAND ../data/com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop.in
 COMMAND = /usr/bin/meson --internal msgfmthelper --msgfmt=/usr/bin/msgfmt ../data/com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop.in data/com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop desktop ../data/../po
 description = Merging$ translations$ for$ data/com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop

build data/com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml: CUSTOM_COMMAND ../data/com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml.in
 COMMAND = /usr/bin/meson --internal msgfmthelper --msgfmt=/usr/bin/msgfmt ../data/com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml.in data/com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml xml ../data/../po
 description = Merging$ translations$ for$ data/com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml

build src/netease-cloud-music-gtk4: CUSTOM_COMMAND ../Cargo.toml ../src/application.rs ../src/config.rs ../src/main.rs ../src/window.rs | /home/<USER>/Videos/netease-cloud-music-gtk/build-aux/cargo.sh
 pool = console
 COMMAND = /bin/sh /home/<USER>/Videos/netease-cloud-music-gtk/build-aux/cargo.sh /home/<USER>/Videos/netease-cloud-music-gtk/_build /home/<USER>/Videos/netease-cloud-music-gtk src/netease-cloud-music-gtk4 release netease-cloud-music-gtk4
 description = Generating$ src/cargo-build$ with$ a$ custom$ command

build netease-cloud-music-gtk4-pot: phony meson-internal__netease-cloud-music-gtk4-pot

build meson-internal__netease-cloud-music-gtk4-pot: CUSTOM_COMMAND 
 COMMAND = /usr/bin/meson --internal gettext pot --pkgname=netease-cloud-music-gtk4 --source-root=/home/<USER>/Videos/netease-cloud-music-gtk/ --subdir=po --extra-args=--from-code=UTF-8@@--add-comments@@--keyword=_@@--keyword=N_@@--keyword=C_:1c,2@@--keyword=NC_:1c,2@@--keyword=g_dcgettext:2@@--keyword=g_dngettext:2,3@@--keyword=g_dpgettext2:2c,3@@--flag=N_:1:pass-c-format@@--flag=C_:2:pass-c-format@@--flag=NC_:2:pass-c-format@@--flag=g_dngettext:2:pass-c-format@@--flag=g_strdup_printf:1:c-format@@--flag=g_string_printf:2:c-format@@--flag=g_string_append_printf:2:c-format@@--flag=g_error_new:3:c-format@@--flag=g_set_error:4:c-format@@--flag=g_markup_printf_escaped:1:c-format@@--flag=g_log:3:c-format@@--flag=g_print:1:c-format@@--flag=g_printerr:1:c-format@@--flag=g_printf:1:c-format@@--flag=g_fprintf:2:c-format@@--flag=g_sprintf:2:c-format@@--flag=g_snprintf:3:c-format --xgettext=/usr/bin/xgettext
 description = Running$ external$ command$ netease-cloud-music-gtk4-pot
 pool = console

build po/zh_CN/LC_MESSAGES/netease-cloud-music-gtk4.mo: CUSTOM_COMMAND ../po/zh_CN.po | /usr/bin/msgfmt
 COMMAND = /usr/bin/msgfmt -o po/zh_CN/LC_MESSAGES/netease-cloud-music-gtk4.mo ../po/zh_CN.po
 description = Building$ translation$ po/zh_CN/LC_MESSAGES/netease-cloud-music-gtk4-zh_CN.mo

build netease-cloud-music-gtk4-gmo: phony  | po/zh_CN/LC_MESSAGES/netease-cloud-music-gtk4.mo

build netease-cloud-music-gtk4-update-po: phony meson-internal__netease-cloud-music-gtk4-update-po

build meson-internal__netease-cloud-music-gtk4-update-po: CUSTOM_COMMAND 
 COMMAND = /usr/bin/meson --internal gettext update_po --pkgname=netease-cloud-music-gtk4 --source-root=/home/<USER>/Videos/netease-cloud-music-gtk/ --subdir=po --extra-args=--from-code=UTF-8@@--add-comments@@--keyword=_@@--keyword=N_@@--keyword=C_:1c,2@@--keyword=NC_:1c,2@@--keyword=g_dcgettext:2@@--keyword=g_dngettext:2,3@@--keyword=g_dpgettext2:2c,3@@--flag=N_:1:pass-c-format@@--flag=C_:2:pass-c-format@@--flag=NC_:2:pass-c-format@@--flag=g_dngettext:2:pass-c-format@@--flag=g_strdup_printf:1:c-format@@--flag=g_string_printf:2:c-format@@--flag=g_string_append_printf:2:c-format@@--flag=g_error_new:3:c-format@@--flag=g_set_error:4:c-format@@--flag=g_markup_printf_escaped:1:c-format@@--flag=g_log:3:c-format@@--flag=g_print:1:c-format@@--flag=g_printerr:1:c-format@@--flag=g_printf:1:c-format@@--flag=g_fprintf:2:c-format@@--flag=g_sprintf:2:c-format@@--flag=g_snprintf:3:c-format --msginit=/usr/bin/msginit --msgmerge=/usr/bin/msgmerge
 description = Running$ external$ command$ netease-cloud-music-gtk4-update-po
 pool = console

# Test rules

build test: phony meson-internal__test

build meson-internal__test: CUSTOM_COMMAND all PHONY
 COMMAND = /usr/bin/meson test --no-rebuild --print-errorlogs
 DESC = Running$ all$ tests.
 pool = console

build benchmark: phony meson-internal__benchmark

build meson-internal__benchmark: CUSTOM_COMMAND all PHONY
 COMMAND = /usr/bin/meson test --benchmark --logbase benchmarklog --num-processes=1 --no-rebuild
 DESC = Running$ benchmark$ suite.
 pool = console

# Install rules

build install: phony meson-internal__install

build meson-internal__install: CUSTOM_COMMAND PHONY | all
 DESC = Installing$ files.
 COMMAND = /usr/bin/meson install --no-rebuild
 pool = console

build dist: phony meson-internal__dist

build meson-internal__dist: CUSTOM_COMMAND PHONY
 DESC = Creating$ source$ packages
 COMMAND = /usr/bin/meson dist
 pool = console

# Suffix

build uninstall: phony meson-internal__uninstall

build meson-internal__uninstall: CUSTOM_COMMAND PHONY
 COMMAND = /usr/bin/meson --internal uninstall
 pool = console

build all: phony meson-test-prereq meson-benchmark-prereq data/netease-cloud-music-gtk4.gresource data/com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop data/com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml src/netease-cloud-music-gtk4 po/zh_CN/LC_MESSAGES/netease-cloud-music-gtk4.mo

build meson-test-prereq: phony data/com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop data/com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml

build meson-benchmark-prereq: phony 

build clean: phony meson-internal__clean

build clean-ctlist: phony meson-internal__clean-ctlist

build meson-internal__clean-ctlist: CUSTOM_COMMAND PHONY
 COMMAND = /usr/bin/meson --internal cleantrees /home/<USER>/Videos/netease-cloud-music-gtk/_build/meson-private/cleantrees.dat
 description = Cleaning$ custom$ target$ directories

build meson-internal__clean: CUSTOM_COMMAND PHONY | clean-ctlist
 COMMAND = /usr/bin/ninja -t clean
 description = Cleaning

build build.ninja: REGENERATE_BUILD ../meson.build ../data/meson.build ../data/icons/meson.build ../src/meson.build ../src/config.rs.in /usr/bin/cp ../po/meson.build meson-private/coredata.dat
 pool = console

build reconfigure: REGENERATE_BUILD PHONY
 pool = console

build ../meson.build ../data/meson.build ../data/icons/meson.build ../src/meson.build ../src/config.rs.in /usr/bin/cp ../po/meson.build meson-private/coredata.dat: phony 

default all

