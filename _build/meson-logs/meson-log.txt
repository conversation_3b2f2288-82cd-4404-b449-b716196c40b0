Build started at 2025-11-02T13:20:00.115592
Main binary: /usr/bin/python3
Build Options: 
Python system: Linux
The Meson build system
Version: 1.5.1
Source dir: /home/<USER>/Videos/netease-cloud-music-gtk
Build dir: /home/<USER>/Videos/netease-cloud-music-gtk/_build
Build type: native build
Project name: netease-cloud-music-gtk4
Project version: 2.5.2
-----------
Detecting compiler via: `cc --version` -> 0
stdout:
cc (Ubuntu 13.3.0-6ubuntu2~24.04) 13.3.0
Copyright (C) 2023 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
-----------
Running command: -cpp -x c -E -dM -
-----
-----------
Detecting linker via: `cc -Wl,--version` -> 0
stdout:
GNU ld (GNU Binutils for Ubuntu) 2.42
Copyright (C) 2024 Free Software Foundation, Inc.
This program is free software; you may redistribute it under the terms of
the GNU General Public License version 3 or (at your option) a later version.
This program has absolutely no warranty.
-----------
stderr:
collect2 version 13.3.0
/usr/bin/ld -plugin /usr/libexec/gcc/x86_64-linux-gnu/13/liblto_plugin.so -plugin-opt=/usr/libexec/gcc/x86_64-linux-gnu/13/lto-wrapper -plugin-opt=-fresolution=/tmp/cckbv4y5.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/13 -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/13/../../.. --version -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crtn.o
-----------
-----------
Detecting compiler via: `rustc --version` -> 0
stdout:
rustc 1.87.0 (17067e9ac 2025-05-09)
-----------
-----------
Called: `rustc -C linker=cc -o /home/<USER>/Videos/netease-cloud-music-gtk/_build/meson-private/rusttest /home/<USER>/Videos/netease-cloud-music-gtk/_build/meson-private/sanity.rs` -> 0
-----------
Called: `rustc -C linker=cc --crate-type staticlib --print native-static-libs /home/<USER>/Videos/netease-cloud-music-gtk/_build/meson-private/sanity.rs` -> 0
stderr:
warning: function `main` is never used
 --> /home/<USER>/Videos/netease-cloud-music-gtk/_build/meson-private/sanity.rs:1:4
  |
1 | fn main() {
  |    ^^^^
  |
  = note: `#[warn(dead_code)]` on by default

note: Link against the following native artifacts when linking against this static library. The order and any duplication can be significant on some platforms.

note: native-static-libs: -lgcc_s -lutil -lrt -lpthread -lm -ldl -lc

warning: 1 warning emitted
-----------
Rust compiler for the host machine: rustc -C linker=cc (rustc 1.87.0)
Rust linker for the host machine: rustc -C linker=cc ld.bfd 2.42
-----------
Detecting compiler via: `cc --version` -> 0
stdout:
cc (Ubuntu 13.3.0-6ubuntu2~24.04) 13.3.0
Copyright (C) 2023 Free Software Foundation, Inc.
This is free software; see the source for copying conditions.  There is NO
warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
-----------
Running command: -cpp -x c -E -dM -
-----
-----------
Detecting linker via: `cc -Wl,--version` -> 0
stdout:
GNU ld (GNU Binutils for Ubuntu) 2.42
Copyright (C) 2024 Free Software Foundation, Inc.
This program is free software; you may redistribute it under the terms of
the GNU General Public License version 3 or (at your option) a later version.
This program has absolutely no warranty.
-----------
stderr:
collect2 version 13.3.0
/usr/bin/ld -plugin /usr/libexec/gcc/x86_64-linux-gnu/13/liblto_plugin.so -plugin-opt=/usr/libexec/gcc/x86_64-linux-gnu/13/lto-wrapper -plugin-opt=-fresolution=/tmp/ccPTFCKn.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --build-id --eh-frame-hdr -m elf_x86_64 --hash-style=gnu --as-needed -dynamic-linker /lib64/ld-linux-x86-64.so.2 -pie -z now -z relro /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/Scrt1.o /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crti.o /usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o -L/usr/lib/gcc/x86_64-linux-gnu/13 -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu -L/usr/lib/gcc/x86_64-linux-gnu/13/../../../../lib -L/lib/x86_64-linux-gnu -L/lib/../lib -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib -L/usr/lib/gcc/x86_64-linux-gnu/13/../../.. --version -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o /usr/lib/gcc/x86_64-linux-gnu/13/../../../x86_64-linux-gnu/crtn.o
-----------
-----------
Detecting compiler via: `rustc --version` -> 0
stdout:
rustc 1.87.0 (17067e9ac 2025-05-09)
-----------
-----------
Called: `rustc -C linker=cc -o /home/<USER>/Videos/netease-cloud-music-gtk/_build/meson-private/rusttest /home/<USER>/Videos/netease-cloud-music-gtk/_build/meson-private/sanity.rs` -> 0
-----------
Called: `rustc -C linker=cc --crate-type staticlib --print native-static-libs /home/<USER>/Videos/netease-cloud-music-gtk/_build/meson-private/sanity.rs` -> 0
stderr:
warning: function `main` is never used
 --> /home/<USER>/Videos/netease-cloud-music-gtk/_build/meson-private/sanity.rs:1:4
  |
1 | fn main() {
  |    ^^^^
  |
  = note: `#[warn(dead_code)]` on by default

note: Link against the following native artifacts when linking against this static library. The order and any duplication can be significant on some platforms.

note: native-static-libs: -lgcc_s -lutil -lrt -lpthread -lm -ldl -lc

warning: 1 warning emitted
-----------
Rust compiler for the build machine: rustc -C linker=cc (rustc 1.87.0)
Rust linker for the build machine: rustc -C linker=cc ld.bfd 2.42
Build machine cpu family: x86_64
Build machine cpu: x86_64
Host machine cpu family: x86_64
Host machine cpu: x86_64
Target machine cpu family: x86_64
Target machine cpu: x86_64
Pkg-config binary missing from cross or native file, or env var undefined.
Trying a default Pkg-config fallback at pkg-config
Found pkg-config: YES (/usr/bin/pkg-config) 1.8.1
Determining dependency 'openssl' with pkg-config executable '/usr/bin/pkg-config'
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --modversion openssl` -> 0
stdout:
3.0.13
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --cflags openssl` -> 0
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs openssl` -> 0
stdout:
-L/usr/lib/x86_64-linux-gnu -lssl -lcrypto
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs openssl` -> 0
stdout:
-lssl -lcrypto
-----------
Run-time dependency openssl found: YES 3.0.13
Determining dependency 'dbus-1' with pkg-config executable '/usr/bin/pkg-config'
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --modversion dbus-1` -> 0
stdout:
1.14.10
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --cflags dbus-1` -> 0
stdout:
-I/usr/include/dbus-1.0 -I/usr/lib/x86_64-linux-gnu/dbus-1.0/include
-----------
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs dbus-1` -> 0
stdout:
-L/usr/lib/x86_64-linux-gnu -ldbus-1
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs dbus-1` -> 0
stdout:
-ldbus-1
-----------
Run-time dependency dbus-1 found: YES 1.14.10
Determining dependency 'glib-2.0' with pkg-config executable '/usr/bin/pkg-config'
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --modversion glib-2.0` -> 0
stdout:
2.80.0
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --cflags glib-2.0` -> 0
stdout:
-I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include
-----------
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs glib-2.0` -> 0
stdout:
-L/usr/lib/x86_64-linux-gnu -lglib-2.0
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs glib-2.0` -> 0
stdout:
-lglib-2.0
-----------
Run-time dependency glib-2.0 found: YES 2.80.0
Determining dependency 'gio-2.0' with pkg-config executable '/usr/bin/pkg-config'
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --modversion gio-2.0` -> 0
stdout:
2.80.0
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --cflags gio-2.0` -> 0
stdout:
-I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -pthread -I/usr/include/libmount -I/usr/include/blkid
-----------
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gio-2.0` -> 0
stdout:
-L/usr/lib/x86_64-linux-gnu -lgio-2.0 -lgobject-2.0 -lglib-2.0
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gio-2.0` -> 0
stdout:
-lgio-2.0 -lgobject-2.0 -lglib-2.0
-----------
Run-time dependency gio-2.0 found: YES 2.80.0
Determining dependency 'gdk-pixbuf-2.0' with pkg-config executable '/usr/bin/pkg-config'
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --modversion gdk-pixbuf-2.0` -> 0
stdout:
2.42.10
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --cflags gdk-pixbuf-2.0` -> 0
stdout:
-I/usr/include/gdk-pixbuf-2.0 -I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -I/usr/include/libpng16 -I/usr/include/x86_64-linux-gnu -I/usr/include/webp -pthread -I/usr/include/libmount -I/usr/include/blkid
-----------
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gdk-pixbuf-2.0` -> 0
stdout:
-L/usr/lib/x86_64-linux-gnu -lgdk_pixbuf-2.0 -lgobject-2.0 -lglib-2.0
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gdk-pixbuf-2.0` -> 0
stdout:
-lgdk_pixbuf-2.0 -lgobject-2.0 -lglib-2.0
-----------
Run-time dependency gdk-pixbuf-2.0 found: YES 2.42.10
Determining dependency 'gtk4' with pkg-config executable '/usr/bin/pkg-config'
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --modversion gtk4` -> 0
stdout:
4.14.5
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --cflags gtk4` -> 0
stdout:
-I/usr/include/gtk-4.0 -I/usr/include/pango-1.0 -I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -I/usr/include/harfbuzz -I/usr/include/freetype2 -I/usr/include/libpng16 -I/usr/include/libmount -I/usr/include/blkid -I/usr/include/fribidi -I/usr/include/cairo -I/usr/include/pixman-1 -I/usr/include/gdk-pixbuf-2.0 -I/usr/include/x86_64-linux-gnu -I/usr/include/webp -I/usr/include/graphene-1.0 -I/usr/lib/x86_64-linux-gnu/graphene-1.0/include -mfpmath=sse -msse -msse2 -pthread
-----------
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gtk4` -> 0
stdout:
-L/usr/lib/x86_64-linux-gnu -lgtk-4 -lpangocairo-1.0 -lpango-1.0 -lharfbuzz -lgdk_pixbuf-2.0 -lcairo-gobject -lcairo -lvulkan -lgraphene-1.0 -lgio-2.0 -lgobject-2.0 -lglib-2.0
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gtk4` -> 0
stdout:
-lgtk-4 -lpangocairo-1.0 -lpango-1.0 -lharfbuzz -lgdk_pixbuf-2.0 -lcairo-gobject -lcairo -lvulkan -lgraphene-1.0 -lgio-2.0 -lgobject-2.0 -lglib-2.0
-----------
Run-time dependency gtk4 found: YES 4.14.5
Determining dependency 'libadwaita-1' with pkg-config executable '/usr/bin/pkg-config'
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --modversion libadwaita-1` -> 0
stdout:
1.5.0
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --cflags libadwaita-1` -> 0
stdout:
-I/usr/include/libadwaita-1 -I/usr/include/gtk-4.0 -I/usr/include/pango-1.0 -I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -I/usr/include/harfbuzz -I/usr/include/freetype2 -I/usr/include/libpng16 -I/usr/include/libmount -I/usr/include/blkid -I/usr/include/fribidi -I/usr/include/cairo -I/usr/include/pixman-1 -I/usr/include/gdk-pixbuf-2.0 -I/usr/include/x86_64-linux-gnu -I/usr/include/webp -I/usr/include/graphene-1.0 -I/usr/lib/x86_64-linux-gnu/graphene-1.0/include -mfpmath=sse -msse -msse2 -I/usr/include/appstream -pthread
-----------
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs libadwaita-1` -> 0
stdout:
-L/usr/lib/x86_64-linux-gnu -ladwaita-1 -lgtk-4 -lpangocairo-1.0 -lpango-1.0 -lharfbuzz -lgdk_pixbuf-2.0 -lcairo-gobject -lcairo -lvulkan -lgraphene-1.0 -lgio-2.0 -lgobject-2.0 -lglib-2.0
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs libadwaita-1` -> 0
stdout:
-ladwaita-1 -lgtk-4 -lpangocairo-1.0 -lpango-1.0 -lharfbuzz -lgdk_pixbuf-2.0 -lcairo-gobject -lcairo -lvulkan -lgraphene-1.0 -lgio-2.0 -lgobject-2.0 -lglib-2.0
-----------
Run-time dependency libadwaita-1 found: YES 1.5.0
Determining dependency 'gstreamer-1.0' with pkg-config executable '/usr/bin/pkg-config'
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --modversion gstreamer-1.0` -> 0
stdout:
1.24.2
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --cflags gstreamer-1.0` -> 0
stdout:
-I/usr/include/gstreamer-1.0 -I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -pthread -I/usr/include/x86_64-linux-gnu
-----------
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gstreamer-1.0` -> 0
stdout:
-L/usr/lib/x86_64-linux-gnu -lgstreamer-1.0 -lgobject-2.0 -lglib-2.0
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gstreamer-1.0` -> 0
stdout:
-lgstreamer-1.0 -lgobject-2.0 -lglib-2.0
-----------
Run-time dependency gstreamer-1.0 found: YES 1.24.2
Determining dependency 'gstreamer-base-1.0' with pkg-config executable '/usr/bin/pkg-config'
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --modversion gstreamer-base-1.0` -> 0
stdout:
1.24.2
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --cflags gstreamer-base-1.0` -> 0
stdout:
-I/usr/include/gstreamer-1.0 -I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -I/usr/include/x86_64-linux-gnu -pthread
-----------
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gstreamer-base-1.0` -> 0
stdout:
-L/usr/lib/x86_64-linux-gnu -lgstbase-1.0 -lgstreamer-1.0 -lgobject-2.0 -lglib-2.0
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gstreamer-base-1.0` -> 0
stdout:
-lgstbase-1.0 -lgstreamer-1.0 -lgobject-2.0 -lglib-2.0
-----------
Run-time dependency gstreamer-base-1.0 found: YES 1.24.2
Determining dependency 'gstreamer-audio-1.0' with pkg-config executable '/usr/bin/pkg-config'
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --modversion gstreamer-audio-1.0` -> 0
stdout:
1.24.2
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --cflags gstreamer-audio-1.0` -> 0
stdout:
-I/usr/include/gstreamer-1.0 -I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -I/usr/include/x86_64-linux-gnu -pthread -I/usr/include/orc-0.4
-----------
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gstreamer-audio-1.0` -> 0
stdout:
-L/usr/lib/x86_64-linux-gnu -lgstaudio-1.0 -lgstbase-1.0 -lgstreamer-1.0 -lgobject-2.0 -lglib-2.0
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gstreamer-audio-1.0` -> 0
stdout:
-lgstaudio-1.0 -lgstbase-1.0 -lgstreamer-1.0 -lgobject-2.0 -lglib-2.0
-----------
Run-time dependency gstreamer-audio-1.0 found: YES 1.24.2
Determining dependency 'gstreamer-play-1.0' with pkg-config executable '/usr/bin/pkg-config'
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --modversion gstreamer-play-1.0` -> 0
stdout:
1.24.2
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --cflags gstreamer-play-1.0` -> 0
stdout:
-I/usr/include/gstreamer-1.0 -I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -I/usr/include/x86_64-linux-gnu -I/usr/include/orc-0.4 -pthread
-----------
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gstreamer-play-1.0` -> 0
stdout:
-L/usr/lib/x86_64-linux-gnu -lgstplay-1.0 -lgstvideo-1.0 -lgstbase-1.0 -lgstreamer-1.0 -lgobject-2.0 -lglib-2.0
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gstreamer-play-1.0` -> 0
stdout:
-lgstplay-1.0 -lgstvideo-1.0 -lgstbase-1.0 -lgstreamer-1.0 -lgobject-2.0 -lglib-2.0
-----------
Run-time dependency gstreamer-play-1.0 found: YES 1.24.2
Determining dependency 'gstreamer-plugins-base-1.0' with pkg-config executable '/usr/bin/pkg-config'
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --modversion gstreamer-plugins-base-1.0` -> 0
stdout:
1.24.2
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --cflags gstreamer-plugins-base-1.0` -> 0
stdout:
-I/usr/include/gstreamer-1.0 -I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -pthread -I/usr/include/x86_64-linux-gnu
-----------
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gstreamer-plugins-base-1.0` -> 0
stdout:
-L/usr/lib/x86_64-linux-gnu -lgstreamer-1.0 -lgobject-2.0 -lglib-2.0
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gstreamer-plugins-base-1.0` -> 0
stdout:
-lgstreamer-1.0 -lgobject-2.0 -lglib-2.0
-----------
Run-time dependency gstreamer-plugins-base-1.0 found: YES 1.24.2
Determining dependency 'gstreamer-plugins-bad-1.0' with pkg-config executable '/usr/bin/pkg-config'
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --modversion gstreamer-plugins-bad-1.0` -> 0
stdout:
1.24.2
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --cflags gstreamer-plugins-bad-1.0` -> 0
stdout:
-I/usr/include/gstreamer-1.0 -I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -I/usr/include/x86_64-linux-gnu -pthread
-----------
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gstreamer-plugins-bad-1.0` -> 0
stdout:
-L/usr/lib/x86_64-linux-gnu -lgstphotography-1.0 -lgstbase-1.0 -lgstreamer-1.0 -lgobject-2.0 -lglib-2.0
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gstreamer-plugins-bad-1.0` -> 0
stdout:
-lgstphotography-1.0 -lgstbase-1.0 -lgstreamer-1.0 -lgobject-2.0 -lglib-2.0
-----------
Run-time dependency gstreamer-plugins-bad-1.0 found: YES 1.24.2
Determining dependency 'gstreamer-bad-audio-1.0' with pkg-config executable '/usr/bin/pkg-config'
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --modversion gstreamer-bad-audio-1.0` -> 0
stdout:
1.24.2
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --cflags gstreamer-bad-audio-1.0` -> 0
stdout:
-I/usr/include/gstreamer-1.0 -I/usr/include/glib-2.0 -I/usr/lib/x86_64-linux-gnu/glib-2.0/include -I/usr/include/x86_64-linux-gnu -pthread -I/usr/include/orc-0.4
-----------
env[PKG_CONFIG_ALLOW_SYSTEM_LIBS]: 1
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gstreamer-bad-audio-1.0` -> 0
stdout:
-L/usr/lib/x86_64-linux-gnu -lgstbadaudio-1.0 -lgstbase-1.0 -lgstreamer-1.0 -lgobject-2.0 -lglib-2.0
-----------
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --libs gstreamer-bad-audio-1.0` -> 0
stdout:
-lgstbadaudio-1.0 -lgstbase-1.0 -lgstreamer-1.0 -lgobject-2.0 -lglib-2.0
-----------
Run-time dependency gstreamer-bad-audio-1.0 found: YES 1.24.2
Dependency gio-2.0 found: YES 2.80.0 (cached)
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --variable=glib_compile_resources gio-2.0` -> 0
stdout:
/usr/bin/glib-compile-resources
-----------
Got pkg-config variable glib_compile_resources : /usr/bin/glib-compile-resources
Program /usr/bin/glib-compile-resources found: YES (/usr/bin/glib-compile-resources)
Program msgfmt found: YES (/usr/bin/msgfmt)
Program desktop-file-validate found: YES (/usr/bin/desktop-file-validate)
Adding test "Validate desktop file"
Program appstreamcli found: YES (/usr/bin/appstreamcli)
Adding test "Validate appstream file"
Program glib-compile-schemas found: YES (/usr/bin/glib-compile-schemas)
Adding test "Validate schema file"
Configuring config.rs using configuration
Running command: /usr/bin/cp /home/<USER>/Videos/netease-cloud-music-gtk/_build/src/config.rs /home/<USER>/Videos/netease-cloud-music-gtk/src/config.rs
--- stdout ---

--- stderr ---


Program /home/<USER>/Videos/netease-cloud-music-gtk/build-aux/cargo.sh found: YES (/bin/sh /home/<USER>/Videos/netease-cloud-music-gtk/build-aux/cargo.sh)
Program msginit found: YES (/usr/bin/msginit)
Program msgmerge found: YES (/usr/bin/msgmerge)
Program xgettext found: YES (/usr/bin/xgettext)
Dependency gio-2.0 found: YES 2.80.0 (cached)
env[PKG_CONFIG_PATH]: 
env[PKG_CONFIG]: /usr/bin/pkg-config
-----------
Called: `/usr/bin/pkg-config --variable=glib_compile_schemas gio-2.0` -> 0
stdout:
/usr/lib/x86_64-linux-gnu/glib-2.0/glib-compile-schemas
-----------
Got pkg-config variable glib_compile_schemas : /usr/lib/x86_64-linux-gnu/glib-2.0/glib-compile-schemas
Program /usr/lib/x86_64-linux-gnu/glib-2.0/glib-compile-schemas found: YES (/usr/lib/x86_64-linux-gnu/glib-2.0/glib-compile-schemas)
Program gtk4-update-icon-cache found: YES (/usr/bin/gtk4-update-icon-cache)
Program update-desktop-database found: YES (/usr/bin/update-desktop-database)
Build targets in project: 8

Found ninja-1.11.1 at /usr/bin/ninja
Failed to guess install tag for /usr/local/share/applications/com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop
Failed to guess install tag for /usr/local/share/metainfo/com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml
Failed to guess install tag for /usr/local/share/glib-2.0/schemas/com.gitee.gmg137.NeteaseCloudMusicGtk4.gschema.xml
Failed to guess install tag for /usr/local/share/icons/hicolor/scalable/apps/com.gitee.gmg137.NeteaseCloudMusicGtk4.svg
Failed to guess install tag for /usr/local/share/icons/hicolor/symbolic/apps/com.gitee.gmg137.NeteaseCloudMusicGtk4-symbolic.svg
Failed to guess install tag for install script: /usr/lib/x86_64-linux-gnu/glib-2.0/glib-compile-schemas /usr/local/share/glib-2.0/schemas
Failed to guess install tag for install script: /usr/bin/gtk4-update-icon-cache -q -t -f /usr/local/share/icons/hicolor
Failed to guess install tag for install script: /usr/bin/update-desktop-database -q /usr/local/share/applications
Failed to guess install tag for /usr/local/share/applications/com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop
Failed to guess install tag for /usr/local/share/metainfo/com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml
Failed to guess install tag for /usr/local/share/glib-2.0/schemas/com.gitee.gmg137.NeteaseCloudMusicGtk4.gschema.xml
Failed to guess install tag for /usr/local/share/icons/hicolor/scalable/apps/com.gitee.gmg137.NeteaseCloudMusicGtk4.svg
Failed to guess install tag for /usr/local/share/icons/hicolor/symbolic/apps/com.gitee.gmg137.NeteaseCloudMusicGtk4-symbolic.svg
Failed to guess install tag for install script: /usr/lib/x86_64-linux-gnu/glib-2.0/glib-compile-schemas /usr/local/share/glib-2.0/schemas
Failed to guess install tag for install script: /usr/bin/gtk4-update-icon-cache -q -t -f /usr/local/share/icons/hicolor
Failed to guess install tag for install script: /usr/bin/update-desktop-database -q /usr/local/share/applications
