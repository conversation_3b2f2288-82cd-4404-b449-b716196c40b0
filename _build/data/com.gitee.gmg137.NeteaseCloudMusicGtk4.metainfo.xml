<?xml version="1.0" encoding="UTF-8"?>
<component type="desktop-application">
  <id>com.gitee.gmg137.NeteaseCloudMusicGtk4</id>
  <developer_name>gmg137</developer_name>
  <metadata_license>CC0-1.0</metadata_license>
  <project_license>GPL-3.0-or-later</project_license>
  <releases>
    <release version="2.5.2" date="2025-05-06" type="stable">
      <description>
        <ul>
          <li>修复gnome48图标异常</li>
          <li>修改debian安装说明</li>
          <li>优化界面设计</li>
          <li>mpris 显示原始图片</li>
          <li>更新依赖版本</li>
          <li>隐藏日推歌单</li>
        </ul>
      </description>
    </release>
    <release version="2.5.1" date="2025-04-20" type="stable">
      <description>
        <ul>
          <li>优化gettext翻译</li>
          <li>优化播放速度</li>
          <li>修复rand编译错误</li>
          <li>更新安装说明</li>
        </ul>
      </description>
    </release>
    <release version="2.5.0" date="2024-11-03" type="stable">
      <description>
        <ul>
          <li>添加歌词滚动播放功能</li>
          <li>持久化音量设置</li>
          <li>锁定gettext版本</li>
          <li>修复相同页面多次创建问题</li>
          <li>修复删除歌曲按钮图标缺失</li>
          <li>在用户名前添加皇冠图标以标示VIP用户</li>
          <li>添加心动模式/智能播放功能</li>
        </ul>
      </description>
    </release>
    <release version="2.4.1" date="2024-08-10"/>
    <release version="2.4.0" date="2024-05-22"/>
  </releases>
  <name>NetEase Cloud Music Gtk4</name>
  <name xml:lang="zh_CN">网易云音乐</name>
  <name xml:lang="zh_TW">網易雲音樂</name>
  <summary>Linux muisc player for NetEase Cloud Music</summary>
  <summary xml:lang="zh_TW">網易雲音樂 Linux 音樂播放器</summary>
  <summary xml:lang="zh_CN">网易云音乐 Linux 音乐播放器</summary>
  <url type="homepage">https://github.com/gmg137/netease-cloud-music-gtk</url>
  <url type="bugtracker">https://github.com/gmg137/netease-cloud-music-gtk/issues</url>
  <recommends>
    <control>pointing</control>
    <control>keyboard</control>
    <control>touch</control>
  </recommends>
  <description>
    <p>netease-cloud-music-gtk is a Netease cloud music player based on Rust + GTK, and developed specifically for Linux systems.</p>
    <p xml:lang="zh_CN">netease-cloud-music-gtk 是使用 Rust + GTK 开发的网易云音乐客户端，专为 Linux 系统打造。</p>
    <p xml:lang="zh_TW">netease-cloud-music-gtk 是使用 Rust + GTK 開發的網易雲音樂客戶端，專為 Linux 系統打造。</p>
  </description>
  <screenshots>
    <screenshot type="default">
      <image>https://gitee.com/gmg137/netease-cloud-music-gtk/raw/master/screenshots/discover.png</image>
    </screenshot>
    <screenshot>
      <image>https://gitee.com/gmg137/netease-cloud-music-gtk/raw/master/screenshots/toplist.png</image>
    </screenshot>
  </screenshots>
  <content_rating type="oars-1.1"/>
  <launchable type="desktop-id">com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop</launchable>
</component>
