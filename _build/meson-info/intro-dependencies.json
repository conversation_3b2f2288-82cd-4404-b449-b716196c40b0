[{"name": "openssl", "type": "pkgconfig", "version": "3.0.13", "compile_args": [], "link_args": ["-lssl", "-lcrypto"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "dbus-1", "type": "pkgconfig", "version": "1.14.10", "compile_args": ["-I/usr/include/dbus-1.0", "-I/usr/lib/x86_64-linux-gnu/dbus-1.0/include"], "link_args": ["-ldbus-1"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "glib-2.0", "type": "pkgconfig", "version": "2.80.0", "compile_args": ["-I/usr/include/glib-2.0", "-I/usr/lib/x86_64-linux-gnu/glib-2.0/include"], "link_args": ["-lglib-2.0"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "gio-2.0", "type": "pkgconfig", "version": "2.80.0", "compile_args": ["-I/usr/include/glib-2.0", "-I/usr/lib/x86_64-linux-gnu/glib-2.0/include", "-pthread", "-I/usr/include/libmount", "-I/usr/include/blkid"], "link_args": ["-lgio-2.0", "-lgobject-2.0", "-lglib-2.0"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "gdk-pixbuf-2.0", "type": "pkgconfig", "version": "2.42.10", "compile_args": ["-I/usr/include/gdk-pixbuf-2.0", "-I/usr/include/glib-2.0", "-I/usr/lib/x86_64-linux-gnu/glib-2.0/include", "-I/usr/include/libpng16", "-I/usr/include/x86_64-linux-gnu", "-I/usr/include/webp", "-pthread", "-I/usr/include/libmount", "-I/usr/include/blkid"], "link_args": ["-lgdk_pixbuf-2.0", "-lgobject-2.0", "-lglib-2.0"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "gtk4", "type": "pkgconfig", "version": "4.14.5", "compile_args": ["-I/usr/include/gtk-4.0", "-I/usr/include/pango-1.0", "-I/usr/include/glib-2.0", "-I/usr/lib/x86_64-linux-gnu/glib-2.0/include", "-I/usr/include/harfbuzz", "-I/usr/include/freetype2", "-I/usr/include/libpng16", "-I/usr/include/libmount", "-I/usr/include/blkid", "-I/usr/include/fribidi", "-I/usr/include/cairo", "-I/usr/include/pixman-1", "-I/usr/include/gdk-pixbuf-2.0", "-I/usr/include/x86_64-linux-gnu", "-I/usr/include/webp", "-I/usr/include/graphene-1.0", "-I/usr/lib/x86_64-linux-gnu/graphene-1.0/include", "-mfpmath=sse", "-msse", "-msse2", "-pthread"], "link_args": ["-lgtk-4", "-lpangocairo-1.0", "-lpango-1.0", "-lharfbuzz", "-lgdk_pixbuf-2.0", "-lcairo-gobject", "-l<PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>", "-lgraphene-1.0", "-lgio-2.0", "-lgobject-2.0", "-lglib-2.0"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "libadwaita-1", "type": "pkgconfig", "version": "1.5.0", "compile_args": ["-I/usr/include/libadwaita-1", "-I/usr/include/gtk-4.0", "-I/usr/include/pango-1.0", "-I/usr/include/glib-2.0", "-I/usr/lib/x86_64-linux-gnu/glib-2.0/include", "-I/usr/include/harfbuzz", "-I/usr/include/freetype2", "-I/usr/include/libpng16", "-I/usr/include/libmount", "-I/usr/include/blkid", "-I/usr/include/fribidi", "-I/usr/include/cairo", "-I/usr/include/pixman-1", "-I/usr/include/gdk-pixbuf-2.0", "-I/usr/include/x86_64-linux-gnu", "-I/usr/include/webp", "-I/usr/include/graphene-1.0", "-I/usr/lib/x86_64-linux-gnu/graphene-1.0/include", "-mfpmath=sse", "-msse", "-msse2", "-I/usr/include/appstream", "-pthread"], "link_args": ["-ladwaita-1", "-lgtk-4", "-lpangocairo-1.0", "-lpango-1.0", "-lharfbuzz", "-lgdk_pixbuf-2.0", "-lcairo-gobject", "-l<PERSON><PERSON>", "-<PERSON><PERSON><PERSON><PERSON>", "-lgraphene-1.0", "-lgio-2.0", "-lgobject-2.0", "-lglib-2.0"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "gstreamer-1.0", "type": "pkgconfig", "version": "1.24.2", "compile_args": ["-I/usr/include/gstreamer-1.0", "-I/usr/include/glib-2.0", "-I/usr/lib/x86_64-linux-gnu/glib-2.0/include", "-pthread", "-I/usr/include/x86_64-linux-gnu"], "link_args": ["-lgstreamer-1.0", "-lgobject-2.0", "-lglib-2.0"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "gstreamer-base-1.0", "type": "pkgconfig", "version": "1.24.2", "compile_args": ["-I/usr/include/gstreamer-1.0", "-I/usr/include/glib-2.0", "-I/usr/lib/x86_64-linux-gnu/glib-2.0/include", "-I/usr/include/x86_64-linux-gnu", "-pthread"], "link_args": ["-lgstbase-1.0", "-lgstreamer-1.0", "-lgobject-2.0", "-lglib-2.0"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "gstreamer-audio-1.0", "type": "pkgconfig", "version": "1.24.2", "compile_args": ["-I/usr/include/gstreamer-1.0", "-I/usr/include/glib-2.0", "-I/usr/lib/x86_64-linux-gnu/glib-2.0/include", "-I/usr/include/x86_64-linux-gnu", "-pthread", "-I/usr/include/orc-0.4"], "link_args": ["-lgstaudio-1.0", "-lgstbase-1.0", "-lgstreamer-1.0", "-lgobject-2.0", "-lglib-2.0"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "gstreamer-play-1.0", "type": "pkgconfig", "version": "1.24.2", "compile_args": ["-I/usr/include/gstreamer-1.0", "-I/usr/include/glib-2.0", "-I/usr/lib/x86_64-linux-gnu/glib-2.0/include", "-I/usr/include/x86_64-linux-gnu", "-I/usr/include/orc-0.4", "-pthread"], "link_args": ["-lgstplay-1.0", "-lgstvideo-1.0", "-lgstbase-1.0", "-lgstreamer-1.0", "-lgobject-2.0", "-lglib-2.0"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "gstreamer-plugins-base-1.0", "type": "pkgconfig", "version": "1.24.2", "compile_args": ["-I/usr/include/gstreamer-1.0", "-I/usr/include/glib-2.0", "-I/usr/lib/x86_64-linux-gnu/glib-2.0/include", "-pthread", "-I/usr/include/x86_64-linux-gnu"], "link_args": ["-lgstreamer-1.0", "-lgobject-2.0", "-lglib-2.0"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "gstreamer-plugins-bad-1.0", "type": "pkgconfig", "version": "1.24.2", "compile_args": ["-I/usr/include/gstreamer-1.0", "-I/usr/include/glib-2.0", "-I/usr/lib/x86_64-linux-gnu/glib-2.0/include", "-I/usr/include/x86_64-linux-gnu", "-pthread"], "link_args": ["-lgstphotography-1.0", "-lgstbase-1.0", "-lgstreamer-1.0", "-lgobject-2.0", "-lglib-2.0"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}, {"name": "gstreamer-bad-audio-1.0", "type": "pkgconfig", "version": "1.24.2", "compile_args": ["-I/usr/include/gstreamer-1.0", "-I/usr/include/glib-2.0", "-I/usr/lib/x86_64-linux-gnu/glib-2.0/include", "-I/usr/include/x86_64-linux-gnu", "-pthread", "-I/usr/include/orc-0.4"], "link_args": ["-lgstbadaudio-1.0", "-lgstbase-1.0", "-lgstreamer-1.0", "-lgobject-2.0", "-lglib-2.0"], "include_directories": [], "sources": [], "extra_files": [], "dependencies": [], "depends": [], "meson_variables": []}]