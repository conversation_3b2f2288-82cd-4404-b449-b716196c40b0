[{"name": "netease-cloud-music-gtk4_gresource", "id": "3a6eb07@@netease-cloud-music-gtk4_gresource@cus", "type": "custom", "defined_in": "/home/<USER>/Videos/netease-cloud-music-gtk/data/meson.build", "filename": ["/home/<USER>/Videos/netease-cloud-music-gtk/_build/data/netease-cloud-music-gtk4.gresource"], "build_by_default": true, "target_sources": [{"language": "unknown", "compiler": ["/usr/bin/glib-compile-resources", "@INPUT@", "--sourcedir", "../data", "--internal", "--generate", "--target", "@OUTPUT@", "--dependency-file", "@DEPFILE@"], "parameters": [], "sources": ["/home/<USER>/Videos/netease-cloud-music-gtk/netease_cloud_music_gtk4.gresource.xml"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": true, "install_filename": ["/usr/local/share/netease-cloud-music-gtk4/netease-cloud-music-gtk4.gresource"]}, {"name": "com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop", "id": "3a6eb07@@com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop@cus", "type": "custom", "defined_in": "/home/<USER>/Videos/netease-cloud-music-gtk/data/meson.build", "filename": ["/home/<USER>/Videos/netease-cloud-music-gtk/_build/data/com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop"], "build_by_default": true, "target_sources": [{"language": "unknown", "compiler": ["/usr/bin/meson", "--internal", "msgfmthelper", "--msgfmt=/usr/bin/msgfmt", "@INPUT@", "@OUTPUT@", "desktop", "../data/../po"], "parameters": [], "sources": ["/home/<USER>/Videos/netease-cloud-music-gtk/com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop.in"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": true, "install_filename": ["/usr/local/share/applications/com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop"]}, {"name": "com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml", "id": "3a6eb07@@com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml@cus", "type": "custom", "defined_in": "/home/<USER>/Videos/netease-cloud-music-gtk/data/meson.build", "filename": ["/home/<USER>/Videos/netease-cloud-music-gtk/_build/data/com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml"], "build_by_default": true, "target_sources": [{"language": "unknown", "compiler": ["/usr/bin/meson", "--internal", "msgfmthelper", "--msgfmt=/usr/bin/msgfmt", "@INPUT@", "@OUTPUT@", "xml", "../data/../po"], "parameters": [], "sources": ["/home/<USER>/Videos/netease-cloud-music-gtk/com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml.in"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": true, "install_filename": ["/usr/local/share/metainfo/com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml"]}, {"name": "cargo-build", "id": "25a6634@@cargo-build@cus", "type": "custom", "defined_in": "/home/<USER>/Videos/netease-cloud-music-gtk/src/meson.build", "filename": ["/home/<USER>/Videos/netease-cloud-music-gtk/_build/src/netease-cloud-music-gtk4"], "build_by_default": true, "target_sources": [{"language": "unknown", "compiler": ["/bin/sh", "/home/<USER>/Videos/netease-cloud-music-gtk/build-aux/cargo.sh", "/home/<USER>/Videos/netease-cloud-music-gtk/_build", "/home/<USER>/Videos/netease-cloud-music-gtk", "@OUTPUT@", "release", "netease-cloud-music-gtk4"], "parameters": [], "sources": ["/home/<USER>/Videos/netease-cloud-music-gtk/Cargo.toml", "/home/<USER>/Videos/netease-cloud-music-gtk/src/application.rs", "/home/<USER>/Videos/netease-cloud-music-gtk/src/config.rs", "/home/<USER>/Videos/netease-cloud-music-gtk/src/main.rs", "/home/<USER>/Videos/netease-cloud-music-gtk/src/window.rs"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": true, "install_filename": ["/usr/local/bin/netease-cloud-music-gtk4"]}, {"name": "netease-cloud-music-gtk4-pot", "id": "6199aec@@netease-cloud-music-gtk4-pot@run", "type": "run", "defined_in": "/home/<USER>/Videos/netease-cloud-music-gtk/po/meson.build", "filename": ["/home/<USER>/Videos/netease-cloud-music-gtk/_build/po/netease-cloud-music-gtk4-pot"], "build_by_default": false, "target_sources": [], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}, {"name": "netease-cloud-music-gtk4-zh_CN.mo", "id": "1320f3e@@netease-cloud-music-gtk4-zh_CN.mo@cus", "type": "custom", "defined_in": "/home/<USER>/Videos/netease-cloud-music-gtk/po/zh_CN/LC_MESSAGES/meson.build", "filename": ["/home/<USER>/Videos/netease-cloud-music-gtk/_build/po/zh_CN/LC_MESSAGES/netease-cloud-music-gtk4.mo"], "build_by_default": true, "target_sources": [{"language": "unknown", "compiler": ["/usr/bin/msgfmt", "-o", "@OUTPUT@", "@INPUT@"], "parameters": [], "sources": ["/home/<USER>/Videos/netease-cloud-music-gtk/po/zh_CN.po"], "generated_sources": []}], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": true, "install_filename": ["/usr/local/share/locale/zh_CN/LC_MESSAGES/netease-cloud-music-gtk4.mo"]}, {"name": "netease-cloud-music-gtk4-gmo", "id": "6199aec@@netease-cloud-music-gtk4-gmo@run", "type": "alias", "defined_in": "/home/<USER>/Videos/netease-cloud-music-gtk/po/meson.build", "filename": ["/home/<USER>/Videos/netease-cloud-music-gtk/_build/po/netease-cloud-music-gtk4-gmo"], "build_by_default": false, "target_sources": [], "extra_files": [], "subproject": null, "dependencies": [], "depends": ["1320f3e@@netease-cloud-music-gtk4-zh_CN.mo@cus"], "installed": false}, {"name": "netease-cloud-music-gtk4-update-po", "id": "6199aec@@netease-cloud-music-gtk4-update-po@run", "type": "run", "defined_in": "/home/<USER>/Videos/netease-cloud-music-gtk/po/meson.build", "filename": ["/home/<USER>/Videos/netease-cloud-music-gtk/_build/po/netease-cloud-music-gtk4-update-po"], "build_by_default": false, "target_sources": [], "extra_files": [], "subproject": null, "dependencies": [], "depends": [], "installed": false}]