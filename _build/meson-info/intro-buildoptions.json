[{"name": "build.cmake_prefix_path", "value": [], "section": "core", "machine": "build", "type": "array", "description": "List of additional prefixes for cmake to search"}, {"name": "build.pkg_config_path", "value": [], "section": "core", "machine": "build", "type": "array", "description": "List of additional paths for pkg-config to search"}, {"name": "auto_features", "value": "auto", "section": "core", "machine": "any", "choices": ["enabled", "disabled", "auto"], "type": "combo", "description": "Override value of all 'auto' features"}, {"name": "backend", "value": "ninja", "section": "core", "machine": "any", "choices": ["ninja", "vs", "vs2010", "vs2012", "vs2013", "vs2015", "vs2017", "vs2019", "vs2022", "xcode", "none"], "type": "combo", "description": "Backend to use"}, {"name": "buildtype", "value": "release", "section": "core", "machine": "any", "choices": ["plain", "debug", "debugoptimized", "release", "minsize", "custom"], "type": "combo", "description": "Build type to use"}, {"name": "cmake_prefix_path", "value": [], "section": "core", "machine": "host", "type": "array", "description": "List of additional prefixes for cmake to search"}, {"name": "debug", "value": false, "section": "core", "machine": "any", "type": "boolean", "description": "Enable debug symbols and other information"}, {"name": "default_library", "value": "shared", "section": "core", "machine": "any", "choices": ["shared", "static", "both"], "type": "combo", "description": "Default library type"}, {"name": "force_fallback_for", "value": [], "section": "core", "machine": "any", "type": "array", "description": "Force fallback for those subprojects"}, {"name": "genvslite", "value": "vs2022", "section": "core", "machine": "any", "choices": ["vs2022"], "type": "combo", "description": "Setup multiple buildtype-suffixed ninja-backend build directories, and a [builddir]_vs containing a Visual Studio meta-backend with multiple configurations that calls into them"}, {"name": "install_umask", "value": 18, "section": "core", "machine": "any", "type": "integer", "description": "De<PERSON>ult umask to apply on permissions of installed files"}, {"name": "layout", "value": "mirror", "section": "core", "machine": "any", "choices": ["mirror", "flat"], "type": "combo", "description": "Build directory layout"}, {"name": "optimization", "value": "3", "section": "core", "machine": "any", "choices": ["plain", "0", "g", "1", "2", "3", "s"], "type": "combo", "description": "Optimization level"}, {"name": "pkg_config_path", "value": [], "section": "core", "machine": "host", "type": "array", "description": "List of additional paths for pkg-config to search"}, {"name": "prefer_static", "value": false, "section": "core", "machine": "any", "type": "boolean", "description": "Whether to try static linking before shared linking"}, {"name": "strip", "value": false, "section": "core", "machine": "any", "type": "boolean", "description": "Strip targets on install"}, {"name": "unity", "value": "off", "section": "core", "machine": "any", "choices": ["on", "off", "subprojects"], "type": "combo", "description": "Unity build"}, {"name": "unity_size", "value": 4, "section": "core", "machine": "any", "type": "integer", "description": "Unity block size"}, {"name": "vsenv", "value": false, "section": "core", "machine": "any", "type": "boolean", "description": "Activate Visual Studio environment"}, {"name": "warning_level", "value": "2", "section": "core", "machine": "any", "choices": ["0", "1", "2", "3", "everything"], "type": "combo", "description": "Compiler warning level to use"}, {"name": "werror", "value": false, "section": "core", "machine": "any", "type": "boolean", "description": "Treat warnings as errors"}, {"name": "wrap_mode", "value": "default", "section": "core", "machine": "any", "choices": ["default", "nofallback", "nodownload", "forcefallback", "nopromote"], "type": "combo", "description": "Wrap mode"}, {"name": "pkgconfig.relocatable", "value": false, "section": "core", "machine": "any", "type": "boolean", "description": "Generate pkgconfig files as relocatable"}, {"name": "python.allow_limited_api", "value": true, "section": "core", "machine": "any", "type": "boolean", "description": "Whether to allow use of the Python Limited API"}, {"name": "python.bytecompile", "value": 0, "section": "core", "machine": "any", "type": "integer", "description": "Whether to compile bytecode"}, {"name": "python.install_env", "value": "prefix", "section": "core", "machine": "any", "choices": ["auto", "prefix", "system", "venv"], "type": "combo", "description": "Which python environment to install to"}, {"name": "python.platli<PERSON>", "value": "", "section": "core", "machine": "any", "type": "string", "description": "Directory for site-specific, platform-specific files."}, {"name": "python.pureli<PERSON>", "value": "", "section": "core", "machine": "any", "type": "string", "description": "Directory for site-specific, non-platform-specific files."}, {"name": "backend_max_links", "value": 0, "section": "backend", "machine": "any", "type": "integer", "description": "Maximum number of linker processes to run or 0 for no limit"}, {"name": "b_colorout", "value": "always", "section": "base", "machine": "any", "choices": ["auto", "always", "never"], "type": "combo", "description": "Use colored output"}, {"name": "b_ndebug", "value": "false", "section": "base", "machine": "any", "choices": ["true", "false", "if-release"], "type": "combo", "description": "Disable asserts"}, {"name": "build.c_args", "value": [], "section": "compiler", "machine": "build", "type": "array", "description": "Extra arguments passed to the c compiler"}, {"name": "build.c_link_args", "value": [], "section": "compiler", "machine": "build", "type": "array", "description": "Extra arguments passed to the c linker"}, {"name": "c_args", "value": [], "section": "compiler", "machine": "host", "type": "array", "description": "Extra arguments passed to the c compiler"}, {"name": "c_link_args", "value": [], "section": "compiler", "machine": "host", "type": "array", "description": "Extra arguments passed to the c linker"}, {"name": "build.rust_args", "value": [], "section": "compiler", "machine": "build", "type": "array", "description": "Extra arguments passed to the rust compiler"}, {"name": "build.rust_link_args", "value": [], "section": "compiler", "machine": "build", "type": "array", "description": "Extra arguments passed to the rust linker"}, {"name": "build.rust_std", "value": "none", "section": "compiler", "machine": "build", "choices": ["none", "2015", "2018", "2021"], "type": "combo", "description": "Rust edition to use"}, {"name": "rust_args", "value": [], "section": "compiler", "machine": "host", "type": "array", "description": "Extra arguments passed to the rust compiler"}, {"name": "rust_link_args", "value": [], "section": "compiler", "machine": "host", "type": "array", "description": "Extra arguments passed to the rust linker"}, {"name": "rust_std", "value": "none", "section": "compiler", "machine": "host", "choices": ["none", "2015", "2018", "2021"], "type": "combo", "description": "Rust edition to use"}, {"name": "bindir", "value": "bin", "section": "directory", "machine": "any", "type": "string", "description": "Executable directory"}, {"name": "datadir", "value": "share", "section": "directory", "machine": "any", "type": "string", "description": "Data file directory"}, {"name": "includedir", "value": "include", "section": "directory", "machine": "any", "type": "string", "description": "Header file directory"}, {"name": "infodir", "value": "share/info", "section": "directory", "machine": "any", "type": "string", "description": "Info page directory"}, {"name": "libdir", "value": "lib/x86_64-linux-gnu", "section": "directory", "machine": "any", "type": "string", "description": "Library directory"}, {"name": "libexecdir", "value": "libexec", "section": "directory", "machine": "any", "type": "string", "description": "Library executable directory"}, {"name": "licensedir", "value": "", "section": "directory", "machine": "any", "type": "string", "description": "Licenses directory"}, {"name": "localedir", "value": "share/locale", "section": "directory", "machine": "any", "type": "string", "description": "Locale data directory"}, {"name": "localstatedir", "value": "/var/local", "section": "directory", "machine": "any", "type": "string", "description": "Localstate data directory"}, {"name": "mandir", "value": "share/man", "section": "directory", "machine": "any", "type": "string", "description": "Manual page directory"}, {"name": "prefix", "value": "/usr/local", "section": "directory", "machine": "any", "type": "string", "description": "Installation prefix"}, {"name": "sbindir", "value": "sbin", "section": "directory", "machine": "any", "type": "string", "description": "System executable directory"}, {"name": "sharedstatedir", "value": "/var/local/lib", "section": "directory", "machine": "any", "type": "string", "description": "Architecture-independent data directory"}, {"name": "sysconfdir", "value": "etc", "section": "directory", "machine": "any", "type": "string", "description": "Sysconf data directory"}, {"name": "errorlogs", "value": true, "section": "test", "machine": "any", "type": "boolean", "description": "Whether to print the logs from failing tests"}, {"name": "stdsplit", "value": true, "section": "test", "machine": "any", "type": "boolean", "description": "Split stdout and stderr in test logs"}]