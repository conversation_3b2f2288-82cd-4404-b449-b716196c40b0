[{"cmd": ["/usr/bin/desktop-file-validate", "data/com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop"], "env": {}, "name": "Validate desktop file", "workdir": null, "timeout": 30, "suite": ["netease-cloud-music-gtk4"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["3a6eb07@@com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop@cus"], "extra_paths": []}, {"cmd": ["/usr/bin/appstreamcli", "validate", "--no-net", "--explain", "data/com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml"], "env": {}, "name": "Validate appstream file", "workdir": null, "timeout": 30, "suite": ["netease-cloud-music-gtk4"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": ["3a6eb07@@com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml@cus"], "extra_paths": []}, {"cmd": ["/usr/bin/glib-compile-schemas", "--strict", "--dry-run", "/home/<USER>/Videos/netease-cloud-music-gtk/data"], "env": {}, "name": "Validate schema file", "workdir": null, "timeout": 30, "suite": ["netease-cloud-music-gtk4"], "is_parallel": true, "priority": 0, "protocol": "exitcode", "depends": [], "extra_paths": []}]