{"meson_version": {"full": "1.5.1", "major": 1, "minor": 5, "patch": 1}, "directories": {"source": "/home/<USER>/Videos/netease-cloud-music-gtk", "build": "/home/<USER>/Videos/netease-cloud-music-gtk/_build", "info": "/home/<USER>/Videos/netease-cloud-music-gtk/_build/meson-info"}, "introspection": {"version": {"full": "1.0.0", "major": 1, "minor": 0, "patch": 0}, "information": {"benchmarks": {"file": "intro-benchmarks.json", "updated": true}, "buildoptions": {"file": "intro-buildoptions.json", "updated": true}, "buildsystem_files": {"file": "intro-buildsystem_files.json", "updated": true}, "compilers": {"file": "intro-compilers.json", "updated": true}, "dependencies": {"file": "intro-dependencies.json", "updated": true}, "installed": {"file": "intro-installed.json", "updated": true}, "install_plan": {"file": "intro-install_plan.json", "updated": true}, "machines": {"file": "intro-machines.json", "updated": true}, "projectinfo": {"file": "intro-projectinfo.json", "updated": true}, "targets": {"file": "intro-targets.json", "updated": true}, "tests": {"file": "intro-tests.json", "updated": true}}}, "build_files_updated": true, "error": false}