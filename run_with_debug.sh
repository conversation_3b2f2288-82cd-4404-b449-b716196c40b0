#!/bin/bash

# 运行网易云音乐GTK4应用程序并显示详细的debug信息
# 特别关注获取歌曲播放链接的过程

echo "启动网易云音乐GTK4应用程序（带debug信息）..."
echo "注意：当你尝试播放歌曲时，会看到详细的API调用过程"
echo "========================================================"
echo ""

# 设置日志级别为debug，并过滤出相关的日志信息
# 使用颜色高亮不同类型的日志
RUST_LOG=debug ./target/debug/netease-cloud-music-gtk4 2>&1 | \
    grep --line-buffered -E "(获取歌曲|API|HTTP|播放链接|songs_url|Song_V1|开始|成功|失败|调用|处理|设置|使用)" | \
    sed -e 's/DEBUG/\x1b[32mDEBUG\x1b[0m/g' \
        -e 's/ERROR/\x1b[31mERROR\x1b[0m/g' \
        -e 's/成功/\x1b[32m成功\x1b[0m/g' \
        -e 's/失败/\x1b[31m失败\x1b[0m/g' \
        -e 's/Song_V1/\x1b[33mSong_V1\x1b[0m/g'
