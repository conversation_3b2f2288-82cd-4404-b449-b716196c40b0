[package]
name = "netease-cloud-music-gtk4"
version = "2.5.2"
edition = "2024"

[dependencies]
gettext-rs = { version = "~0.7", features = ["gettext-system"] }
gtk = { version = "~0.9", package = "gtk4" }
once_cell = "~1.19"
qrcode-generator = "~5.0"
# ncm-api = { git = "https://github.com/gmg137/netease-cloud-music-api.git", branch = "dev", package = "netease-cloud-music-api" }
ncm-api = { git = "https://gitee.com/gmg137/netease-cloud-music-api.git", tag = "1.5.1", package = "netease-cloud-music-api" }
anyhow = "~1.0"
gstreamer = "~0.23"
gstreamer-play = "~0.23"
fastrand = "~2.1"
mpris-server = "~0.9"
regex = "~1.10"
log = "~0.4"
env_logger = "~0.11"
cookie_store = "~0.21"
chrono = "~0.4"
async-channel = "~2.3"
reqwest = { version = "~0.12", features = ["json"] }
serde = { version = "~1.0", features = ["derive"] }
serde_json = "~1.0"

[dependencies.adw]
package = "libadwaita"
version = "~0.7"
features = ["v1_5"]

# cargo deb 打包配置
# 打包前在__build目录下执行: meson configure --prefix=/usr && ninja
[package.metadata.deb]
maintainer = "gmg137 <<EMAIL>>"
copyright = "2024, gmg137 <<EMAIL>>"
license-file = ["COPYING", "0"]
extended-description = """\
netease-cloud-music-gtk4 是基于 GTK4 + Libadwaita 构造的网易云音乐播放器，\
专为 Linux 系统打造，已在 openSUSE Tumbleweed + GNOME 环境下测试。"""
depends = "$auto"
section = "sound"
assets = [
    [
        "__build/src/netease-cloud-music-gtk4",
        "usr/bin/",
        "755",
    ],
    [
        "__build/data/com.gitee.gmg137.NeteaseCloudMusicGtk4.desktop",
        "usr/share/applications/",
        "644",
    ],
    [
        "__build/data/netease-cloud-music-gtk4.gresource",
        "usr/share/netease-cloud-music-gtk4/",
        "644",
    ],
    [
        "data/icons/hicolor/scalable/apps/com.gitee.gmg137.NeteaseCloudMusicGtk4.svg",
        "usr/share/icons/hicolor/scalable/apps/",
        "644",
    ],
    [
        "data/icons/hicolor/symbolic/apps/com.gitee.gmg137.NeteaseCloudMusicGtk4-symbolic.svg",
        "usr/share/icons/hicolor/symbolic/apps/",
        "644",
    ],
    [
        "__build/po/zh_CN/LC_MESSAGES/netease-cloud-music-gtk4.mo",
        "usr/share/locale/zh_CN/LC_MESSAGES/",
        "644",
    ],
    [
        "data/com.gitee.gmg137.NeteaseCloudMusicGtk4.gschema.xml",
        "usr/share/glib-2.0/schemas/",
        "644",
    ],
    [
        "__build/data/com.gitee.gmg137.NeteaseCloudMusicGtk4.metainfo.xml",
        "usr/share/metainfo/",
        "644",
    ],
]
